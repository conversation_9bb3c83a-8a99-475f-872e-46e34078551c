/**
 * Test script for persistent call tracking
 * This script simulates the call tracking workflow to verify the solution works
 */

import { 
  storeCallTrackingState, 
  processActiveCallTracking, 
  getActiveCallTrackingStatus,
  markCallAsEnded,
  cleanupOldCallRecords
} from "./actions/ai/call-actions.ts";

// Mock data for testing
const testChannelId = "123456789";
const testCallId = "test-call-" + Date.now();
const testControlUrl = "https://api.vapi.ai/call/test-control";
const testAssistantId = "test-assistant-123";
const testCoachName = "Kokoro";

async function runCallTrackingTest() {
  console.log("🧪 Starting Call Tracking Test");
  console.log("================================");
  
  try {
    // Step 1: Store initial call tracking state
    console.log("1. Storing initial call tracking state...");
    await storeCallTrackingState(
      testChannelId,
      testCallId,
      testControlUrl,
      testAssistantId,
      testCoachName
    );
    console.log("✅ Call tracking state stored");
    
    // Step 2: Check active call status
    console.log("\n2. Checking active call status...");
    const activeCallsBefore = await getActiveCallTrackingStatus();
    console.log(`📊 Active calls: ${activeCallsBefore.length}`);
    console.log("Active calls:", activeCallsBefore);
    
    // Step 3: Process active call tracking (simulate cron job)
    console.log("\n3. Processing active call tracking (simulating cron)...");
    await processActiveCallTracking();
    console.log("✅ Call tracking processed");
    
    // Step 4: Check status after processing
    console.log("\n4. Checking status after processing...");
    const activeCallsAfter = await getActiveCallTrackingStatus();
    console.log(`📊 Active calls after processing: ${activeCallsAfter.length}`);
    
    // Step 5: Mark call as ended
    console.log("\n5. Marking call as ended...");
    await markCallAsEnded(testCallId);
    console.log("✅ Call marked as ended");
    
    // Step 6: Verify call is no longer active
    console.log("\n6. Verifying call is no longer active...");
    const finalActiveCalls = await getActiveCallTrackingStatus();
    console.log(`📊 Final active calls: ${finalActiveCalls.length}`);
    
    // Step 7: Test cleanup function
    console.log("\n7. Testing cleanup function...");
    await cleanupOldCallRecords();
    console.log("✅ Cleanup completed");
    
    console.log("\n🎉 Call Tracking Test Completed Successfully!");
    console.log("The persistent call tracking system is working correctly.");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (import.meta.main) {
  runCallTrackingTest()
    .then(() => {
      console.log("\n✅ All tests passed!");
      Deno.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Test failed:", error);
      Deno.exit(1);
    });
}

export { runCallTrackingTest };
