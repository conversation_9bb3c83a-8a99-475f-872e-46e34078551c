{"expo": {"name": "awaken-mobile", "slug": "awaken-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "awakenmobile", "userInterfaceStyle": "automatic", "newArchEnabled": true, "extra": {"router": {}, "eas": {"projectId": "41a378f0-290a-4362-a833-19f1a0f4adc5"}}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.bruteforcekid.awaken-mobile", "icon": "./assets/images/icon.png", "buildNumber": "1"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.bruteforcekid.awakenmobile"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router", "expo-dev-client", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-localization", ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}]], "experiments": {"typedRoutes": true}, "owner": "bruteforcekid", "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/41a378f0-290a-4362-a833-19f1a0f4adc5"}}}