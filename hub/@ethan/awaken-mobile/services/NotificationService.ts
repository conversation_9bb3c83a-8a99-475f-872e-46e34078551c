import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import * as Constants from 'expo-constants';
import { Platform } from 'react-native';

// Configure how notifications are handled when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: () => Promise.resolve({
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface PushToken {
  token: string;
  type: 'expo' | 'apns' | 'fcm';
}

export class NotificationService {
  private static instance: NotificationService;
  private pushToken: PushToken | null = null;
  private webViewRef: any = null;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Set WebView reference for navigation
   */
  public setWebViewRef(webViewRef: any): void {
    this.webViewRef = webViewRef;
  }

  /**
   * Send navigation command to WebView
   */
  private sendNavigationToWebView(navigationData: any): void {
    if (this.webViewRef?.current) {
      const message = JSON.stringify(navigationData);
      this.webViewRef.current.postMessage(message);
      console.log('[NAVIGATION] Sent navigation command to WebView:', navigationData);
    } else {
      console.warn('[NAVIGATION] WebView reference not available for navigation');
    }
  }

  /**
   * Request notification permissions and get push token
   */
  public async requestPermissionsAndGetToken(): Promise<PushToken | null> {
    try {
      // Log environment information for debugging
      console.log('[PUSH_TOKEN] Environment check:');
      console.log('[PUSH_TOKEN] - Device.isDevice:', Device.isDevice);
      console.log('[PUSH_TOKEN] - Platform.OS:', Platform.OS);
      console.log('[PUSH_TOKEN] - Constants.appOwnership:', Constants.appOwnership);
      console.log('[PUSH_TOKEN] - Constants.executionEnvironment:', Constants.executionEnvironment);
      console.log('[PUSH_TOKEN] - Is Development Build:', Constants.appOwnership !== 'expo');
      console.log('[PUSH_TOKEN] - Is Expo Go:', Constants.appOwnership === 'expo');

      // Check if device supports push notifications
      if (!Device.isDevice) {
        console.log('[PUSH_TOKEN] Push notifications only work on physical devices');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('[PUSH_TOKEN] Failed to get push token for push notification!');
        return null;
      }

      // Get the push token
      try {
        console.log('[PUSH_TOKEN] Attempting to get push token...');
        console.log('[PUSH_TOKEN] Device info:', { isDevice: Device.isDevice, platform: Platform.OS });

        // Try to get project ID from Constants - NO hardcoded fallback
        const getProjectId = () => {
          if (Constants.expoConfig?.extra?.eas?.projectId) {
            return Constants.expoConfig.extra.eas.projectId;
          }
          if (Constants.manifest?.extra?.eas?.projectId) {
            return Constants.manifest.extra.eas.projectId;
          }
          if (Constants.manifest2?.extra?.eas?.projectId) {
            return Constants.manifest2.extra.eas.projectId;
          }
          return null; // No hardcoded fallback
        };

        const projectId = getProjectId();
        console.log('[PUSH_TOKEN] Project ID from config:', projectId || 'none');

        // Always use Expo push tokens - this project strictly uses Expo push service
        // For development builds, try without project ID first
        let tokenData;

        try {
          // Try without project ID first (works for development builds)
          console.log('[PUSH_TOKEN] Trying to get Expo token without project ID...');
          tokenData = await Notifications.getExpoPushTokenAsync();
          console.log('[PUSH_TOKEN] Expo push token obtained successfully (no project ID)');
        } catch (noProjectIdError) {
          console.log('[PUSH_TOKEN] Failed without project ID, trying with project ID...');
          console.log('[PUSH_TOKEN] Error without project ID:', noProjectIdError.message);

          if (projectId) {
            // Use the project ID from configuration
            console.log('[PUSH_TOKEN] Using project ID:', projectId);
            tokenData = await Notifications.getExpoPushTokenAsync({
              projectId: projectId,
            });
            console.log('[PUSH_TOKEN] Expo push token obtained successfully (with project ID)');
          } else {
            console.error('[PUSH_TOKEN] No project ID available and token generation failed without it');
            console.error('[PUSH_TOKEN] For development builds, this should work without a project ID');
            throw noProjectIdError;
          }
        }

        this.pushToken = {
          token: tokenData.data,
          type: 'expo'
        };

        console.log('[PUSH_TOKEN] Push token obtained successfully:', this.pushToken.token.substring(0, 20) + '...');
        console.log('[PUSH_TOKEN] Token type:', this.pushToken.type);
        return this.pushToken;
      } catch (tokenError) {
        // Log the full error for debugging
        console.error('[PUSH_TOKEN] Full error details:', tokenError);
        console.error('[PUSH_TOKEN] Error message:', tokenError.message);

        const errorString = String(tokenError);
        const errorMessage = tokenError.message || '';

        // Handle specific error cases
        if (errorString.includes('EXPERIENCE_NOT_FOUND') ||
            errorMessage.includes('does not exist')) {
          console.error('[PUSH_TOKEN] Project ID does not exist in Expo. This is a development build configuration issue.');
          console.error('[PUSH_TOKEN] The project ID in app.json is invalid or the project was deleted.');
          console.error('[PUSH_TOKEN] For development builds, you can remove the project ID from app.json.');
          return null;
        }

        // Handle Expo Go limitations (check app ownership to be sure)
        if (errorMessage.includes('not supported in Expo Go') ||
            errorMessage.includes('Expo Go') ||
            Constants.appOwnership === 'expo') {
          console.warn('[PUSH_TOKEN] Push notifications not supported in Expo Go. Use a development build for full functionality.');
          console.warn('[PUSH_TOKEN] For testing, you can use the web test interface at /test-notifications');
          return null;
        }

        // Handle other validation errors
        if (errorString.includes('VALIDATION_ERROR') ||
            errorString.includes('Invalid uuid')) {
          console.error('[PUSH_TOKEN] Invalid project configuration. Check your EAS project setup.');
          return null;
        }

        // Log other errors but don't crash
        console.error('[PUSH_TOKEN] Unexpected error getting push token:', errorMessage);
        return null;
      }

    } catch (error) {
      console.error('[PUSH_TOKEN] Error getting push token:', error);
      return null;
    }
  }

  /**
   * Register push token with backend
   */
  public async registerTokenWithBackend(channelId: string, token: PushToken): Promise<boolean> {
    try {
      const baseUrl = process.env.EXPO_PUBLIC_SOURCE_URI || 'https://awaken.is';



      const response = await fetch(`${baseUrl}/api/push-tokens/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId,
          token: token.token,
          type: token.type,
          platform: Platform.OS,
          // Add additional metadata for better debugging
          deviceInfo: {
            isDevice: Device.isDevice,
            appOwnership: Constants.appOwnership,
            executionEnvironment: Constants.executionEnvironment,
          }
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to register token: ${response.status} - ${errorText}`);
      }

      // Try to parse JSON response, but handle cases where it's not JSON
      let result;
      const responseText = await response.text();
      try {
        result = JSON.parse(responseText);
        console.log('[PUSH_TOKEN] Push token registered successfully with backend:', result);
      } catch (parseError) {
        console.log('[PUSH_TOKEN] Backend response (not JSON):', responseText);
        console.log('[PUSH_TOKEN] Push token registration completed (non-JSON response)');
        result = { success: true, response: responseText };
      }

      return true;
    } catch (error) {
      console.error('[PUSH_TOKEN] Error registering token with backend:', error);
      return false;
    }
  }

  /**
   * Initialize notification service - call this when user authenticates
   */
  public async initialize(channelId: string): Promise<void> {
    try {

      const token = await this.requestPermissionsAndGetToken();
      if (token) {
        const success = await this.registerTokenWithBackend(channelId, token);
        // Token registration handled silently
      } else {
        const isExpoGo = Constants.appOwnership === 'expo';
        if (isExpoGo) {
          console.log(`[PUSH_TOKEN] No push token available for channelId: ${channelId} (this is normal in Expo Go)`);
        } else {
          console.log(`[PUSH_TOKEN] No push token available for channelId: ${channelId} (check configuration)`);
        }
      }
    } catch (error) {
      console.error('[PUSH_TOKEN] Error initializing notification service:', error);
    }
  }

  /**
   * Set up notification listeners
   */
  public setupNotificationListeners() {
    // Handle notification received while app is in foreground
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received in foreground:', notification);
    });

    // Handle notification response (user tapped notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);

      // Handle navigation based on notification data
      const data = response.notification.request.content.data;
      if (data?.coachName) {
        // Navigate to specific coach conversation
        console.log('Navigate to coach:', data.coachName);

        // Send navigation command to WebView
        this.sendNavigationToWebView({
          type: 'NAVIGATE_TO_COACH',
          coachName: data.coachName,
          notificationData: data
        });
      }
    });

    return {
      foregroundSubscription,
      responseSubscription,
    };
  }

  /**
   * Get current push token
   */
  public getCurrentToken(): PushToken | null {
    return this.pushToken;
  }

  /**
   * Clear stored token (for logout)
   */
  public clearToken(): void {
    this.pushToken = null;
  }
}

export default NotificationService.getInstance();
