import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

export interface AudioRecordingResult {
  uri: string;
  duration: number;
  size: number;
}

export class AudioRecordingService {
  private static instance: AudioRecordingService;
  private recording: Audio.Recording | null = null;
  private isRecording = false;
  private webViewRef: any = null;

  private constructor() {}

  public static getInstance(): AudioRecordingService {
    if (!AudioRecordingService.instance) {
      AudioRecordingService.instance = new AudioRecordingService();
    }
    return AudioRecordingService.instance;
  }

  /**
   * Set WebView reference for communication
   */
  public setWebViewRef(webViewRef: any): void {
    this.webViewRef = webViewRef;
  }

  /**
   * Send message to WebView
   */
  private sendMessageToWebView(message: any): void {
    console.log('[AUDIO] Sending message to WebView:', message);
    if (this.webViewRef?.current) {
      this.webViewRef.current.postMessage(JSON.stringify(message));
      console.log('[AUDIO] Message sent successfully');
    } else {
      console.warn('[AUDIO] WebView ref not available');
    }
  }

  /**
   * Request audio recording permissions
   */
  public async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('[AUDIO] Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Start audio recording
   */
  public async startRecording(): Promise<boolean> {
    console.log('[AUDIO] startRecording called');

    // Send immediate test message
    this.sendMessageToWebView({
      type: 'AUDIO_RECORDING_STARTED'
    });

    try {
      if (this.isRecording) {
        console.warn('[AUDIO] Recording already in progress');
        return false;
      }

      // Request permissions first
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.error('[AUDIO] Audio recording permission denied');
        this.sendMessageToWebView({
          type: 'AUDIO_RECORDING_ERROR',
          error: 'Permission denied'
        });
        return false;
      }

      // Set audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });

      // Create recording
      this.recording = new Audio.Recording();

      const recordingOptions = {
        android: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
          audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
          audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
      };

      await this.recording.prepareToRecordAsync(recordingOptions);
      await this.recording.startAsync();

      this.isRecording = true;
      console.log('[AUDIO] Recording started successfully');
      return true;

    } catch (error) {
      console.error('[AUDIO] Error starting recording:', error);
      this.sendMessageToWebView({
        type: 'AUDIO_RECORDING_ERROR',
        error: error.message || 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Stop audio recording
   */
  public async stopRecording(): Promise<AudioRecordingResult | null> {
    try {
      if (!this.isRecording || !this.recording) {
        console.warn('[AUDIO] No recording in progress');
        return null;
      }

      await this.recording.stopAndUnloadAsync();
      const uri = this.recording.getURI();

      if (!uri) {
        console.error('[AUDIO] No recording URI available');
        return null;
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(uri);
      const status = await this.recording.getStatusAsync();

      const result: AudioRecordingResult = {
        uri,
        duration: status.durationMillis || 0,
        size: fileInfo.exists ? fileInfo.size || 0 : 0
      };

      this.isRecording = false;
      this.recording = null;

      // Notify WebView with recording result
      this.sendMessageToWebView({
        type: 'AUDIO_RECORDING_COMPLETED',
        result
      });

      console.log('[AUDIO] Recording completed:', result);
      return result;

    } catch (error) {
      console.error('[AUDIO] Error stopping recording:', error);
      this.sendMessageToWebView({
        type: 'AUDIO_RECORDING_ERROR',
        error: error.message || 'Unknown error'
      });
      return null;
    }
  }

  /**
   * Cancel current recording
   */
  public async cancelRecording(): Promise<void> {
    try {
      if (this.isRecording && this.recording) {
        await this.recording.stopAndUnloadAsync();

        // Delete the recording file
        const uri = this.recording.getURI();
        if (uri) {
          await FileSystem.deleteAsync(uri, { idempotent: true });
        }
      }

      this.isRecording = false;
      this.recording = null;

      // Notify WebView that recording was cancelled
      this.sendMessageToWebView({
        type: 'AUDIO_RECORDING_CANCELLED'
      });

      console.log('[AUDIO] Recording cancelled');
    } catch (error) {
      console.error('[AUDIO] Error cancelling recording:', error);
    }
  }

  /**
   * Get current recording status
   */
  public getRecordingStatus(): { isRecording: boolean } {
    return { isRecording: this.isRecording };
  }

  /**
   * Convert audio file to base64 for upload
   */
  public async audioFileToBase64(uri: string): Promise<string | null> {
    try {
      const base64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      return base64;
    } catch (error) {
      console.error('[AUDIO] Error converting to base64:', error);
      return null;
    }
  }

  /**
   * Clean up resources
   */
  public async cleanup(): Promise<void> {
    if (this.isRecording) {
      await this.cancelRecording();
    }
  }
}

export default AudioRecordingService.getInstance();
