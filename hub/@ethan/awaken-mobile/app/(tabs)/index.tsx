import { Linking, Platform, StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { WebView } from "react-native-webview";
import React, { useRef, useEffect, useState } from "react";
import * as Localization from 'expo-localization';
import { useNotifications } from '@/hooks/useNotifications';
import NotificationService from '@/services/NotificationService';
import { AudioRecordingService } from '@/services/AudioRecordingService';


// Get region information
const userRegion = Localization.region; // e.g., "US", "GB", "FR"
const userLocale = Localization.locale; // e.g., "en-US"
const userCurrency = Localization.currency; // e.g., "USD"

// JavaScript to inject into the WebView to enable Expo detection
const INJECTED_JAVASCRIPT = `
  // Mark this environment as Expo
  window.__EXPO_ENV__ = true;
  window.expo = {
    isExpo: true,
    platform: '${Platform.OS}',
    region: '${userRegion}',
    locale: '${userLocale}',
    currency: '${userCurrency}',
  };

  // Add platform information (legacy support)
  window.__EXPO_PLATFORM__ = '${Platform.OS}';
  window.__EXPO_REGION__ = '${userRegion}';

  window.getExpoRegion = function() {
    return window.expo ? window.expo.region : null;
  };

  // Add notification bridge for push token registration
  window.expoPushNotifications = {
    registerToken: function(channelId) {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'REGISTER_PUSH_TOKEN',
          channelId: channelId
        }));
      }
    }
  };

  // Add native audio recording bridge
  window.expoAudioRecording = {
    startRecording: function() {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'START_AUDIO_RECORDING'
        }));
      }
    },
    stopRecording: function() {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'STOP_AUDIO_RECORDING'
        }));
      }
    },
    cancelRecording: function() {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'CANCEL_AUDIO_RECORDING'
        }));
      }
    }
  };

  // Listen for navigation commands from native side
  document.addEventListener('message', function(event) {
    try {
      const data = JSON.parse(event.data);
      if (data.type === 'NAVIGATE_TO_COACH' && data.coachName) {
        console.log('[NAVIGATION] Received navigation command:', data);
        // Navigate to coach conversation
        // This would need to be implemented based on your routing system
        // For example: window.location.href = '/chat/' + data.coachName;
        window.location.href = '/chat/';
      }
    } catch (error) {
      console.error('[NAVIGATION] Error parsing navigation message:', error);
    }
  });



  true; // Required for injectedJavaScript
`;

export default function HomeScreen() {
  const webViewRef = useRef(null);

  // Set WebView reference in services for communication
  useEffect(() => {
    NotificationService.getInstance().setWebViewRef(webViewRef);
    AudioRecordingService.getInstance().setWebViewRef(webViewRef);
  }, []);


  // Set up notifications
  useNotifications({
    onNotificationReceived: (notification) => {
      console.log('Notification received in HomeScreen:', notification);
    },
    onNotificationResponse: (response) => {
      console.log('Notification tapped in HomeScreen:', response);
      // Could navigate to specific coach conversation here
    }
  });

  // Handle messages from WebView
  const handleWebViewMessage = (event: any) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);

      if (message.type === 'REGISTER_PUSH_TOKEN' && message.channelId) {
        console.log('Registering push token for channelId:', message.channelId);
        NotificationService.initialize(message.channelId);
      } else if (message.type === 'START_AUDIO_RECORDING') {
        console.log('Starting native audio recording');
        AudioRecordingService.getInstance().startRecording();
      } else if (message.type === 'STOP_AUDIO_RECORDING') {
        console.log('Stopping native audio recording');
        AudioRecordingService.getInstance().stopRecording();
      } else if (message.type === 'CANCEL_AUDIO_RECORDING') {
        console.log('Cancelling native audio recording');
        AudioRecordingService.getInstance().cancelRecording();
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };



  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri: process.env.EXPO_PUBLIC_SOURCE_URI || "https://awaken.is",
          headers: {
            'ngrok-skip-browser-warning': 'true',
          },
        }}
        ref={webViewRef}
        style={styles.webView}
        originWhitelist={["*"]}
        mediaPlaybackRequiresUserAction={false}
        domStorageEnabled
        javaScriptEnabled
        javaScriptCanOpenWindowsAutomatically
        mixedContentMode="always"
        allowsFullscreenVideo
        allowsInlineMediaPlayback
        geolocationEnabled
        keyboardDisplayRequiresUserAction={false}
        pullToRefreshEnabled
        allowsBackForwardNavigationGestures
        injectedJavaScript={INJECTED_JAVASCRIPT}
        onMessage={handleWebViewMessage}
        scalePageToFit={false}
        startInLoadingState={true}
        userAgent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_7_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1 ExpoWebView"
        scalesPageToFit={(Platform.OS === "ios") ? false : true}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  webView: {
    flex: 1,
  },

});